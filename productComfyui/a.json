{"last_node_id": 15, "last_link_id": 25, "nodes": [{"id": 1, "type": "CheckpointLoaderSimple", "pos": [50, 50], "size": {"0": 315, "1": 98}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [2], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [3, 20], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["flux-schnell.safetensors"], "color": "#232", "bgcolor": "#353"}, {"id": 2, "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pos": [400, 50], "size": {"0": 315, "1": 126}, "flags": {}, "order": 1, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}, {"name": "clip", "type": "CLIP", "link": 2}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [4], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [5, 6], "slot_index": 1}], "properties": {"Node name for S&R": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "widgets_values": ["ACE++ComfyUI_Subject.safetensors", 1.0, 1.0], "color": "#432", "bgcolor": "#653"}, {"id": 3, "type": "CLIPTextEncode", "pos": [750, 50], "size": {"0": 450, "1": 220}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 5}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["maintain the device features, professional product photography of a woman's hand elegantly holding a sleek green metallic beauty device, clean white background, soft natural lighting, marketing style, high quality, detailed hand positioning"], "color": "#232", "bgcolor": "#353"}, {"id": 4, "type": "CLIPTextEncode", "pos": [750, 300], "size": {"0": 450, "1": 180}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 6}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [8], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["blurry, low quality, deformed, ugly, distorted, bad hands, extra fingers, missing fingers, awkward grip, unnatural hand position, poor lighting, cluttered background"], "color": "#322", "bgcolor": "#533"}, {"id": 5, "type": "LoadImage", "pos": [50, 200], "size": {"0": 315, "1": 314}, "flags": {}, "order": 4, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": [], "slot_index": 1}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["product_device.png", "image"], "color": "#432", "bgcolor": "#653"}, {"id": 6, "type": "LoadImage", "pos": [400, 220], "size": {"0": 315, "1": 314}, "flags": {}, "order": 5, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [9, 21], "slot_index": 0}, {"name": "MASK", "type": "MASK", "links": [10], "slot_index": 1}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["hand_background.png", "image"], "color": "#432", "bgcolor": "#653"}, {"id": 7, "type": "VAEEncodeForInpaint", "pos": [750, 550], "size": {"0": 315, "1": 98}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 9}, {"name": "vae", "type": "VAE", "link": 3}, {"name": "mask", "type": "MASK", "link": 10}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [11], "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncodeForInpaint"}, "color": "#432", "bgcolor": "#653"}, {"id": 8, "type": "K<PERSON><PERSON><PERSON>", "pos": [1250, 50], "size": {"0": 315, "1": 474}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 4}, {"name": "positive", "type": "CONDITIONING", "link": 7}, {"name": "negative", "type": "CONDITIONING", "link": 8}, {"name": "latent_image", "type": "LATENT", "link": 11}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [12], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [42, "randomize", 10, 2.0, "dpmpp_2m_sde", "karras", 1.0], "color": "#323", "bgcolor": "#535"}, {"id": 9, "type": "VAEDecode", "pos": [1600, 50], "size": {"0": 210, "1": 46}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 12}, {"name": "vae", "type": "VAE", "link": 20}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [13, 22], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}, "color": "#432", "bgcolor": "#653"}, {"id": 10, "type": "PreviewImage", "pos": [1850, 50], "size": {"0": 315, "1": 314}, "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 13}], "properties": {"Node name for S&R": "PreviewImage"}, "color": "#232", "bgcolor": "#353"}, {"id": 11, "type": "SaveImage", "pos": [2200, 50], "size": {"0": 315, "1": 270}, "flags": {}, "order": 10, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 25}], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["hand_product_result"], "color": "#232", "bgcolor": "#353"}, {"id": 12, "type": "ImageScale", "pos": [1100, 600], "size": {"0": 315, "1": 130}, "flags": {}, "order": 11, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 21}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [23], "slot_index": 0}], "properties": {"Node name for S&R": "ImageScale"}, "widgets_values": ["lanc<PERSON>s", 1024, 1024, "center"], "color": "#432", "bgcolor": "#653"}, {"id": 13, "type": "ImageBlend", "pos": [1600, 400], "size": {"0": 315, "1": 102}, "flags": {}, "order": 12, "mode": 0, "inputs": [{"name": "image1", "type": "IMAGE", "link": 22}, {"name": "image2", "type": "IMAGE", "link": 23}, {"name": "mask", "type": "MASK", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [24], "slot_index": 0}], "properties": {"Node name for S&R": "ImageBlend"}, "widgets_values": [0.85, "normal"], "color": "#432", "bgcolor": "#653"}, {"id": 14, "type": "ColorCorrect", "pos": [1950, 400], "size": {"0": 315, "1": 174}, "flags": {}, "order": 13, "mode": 0, "inputs": [{"name": "image", "type": "IMAGE", "link": 24}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [25], "slot_index": 0}], "properties": {"Node name for S&R": "ColorCorrect"}, "widgets_values": [1.0, 1.05, 1.0, 0.02, 0.0, 1.0, 1.0], "color": "#432", "bgcolor": "#653"}, {"id": 15, "type": "Note", "pos": [50, 600], "size": {"0": 400, "1": 200}, "flags": {}, "order": 14, "mode": 0, "properties": {"Node name for S&R": "Note"}, "widgets_values": ["HAND-HELD PRODUCT WORKFLOW INSTRUCTIONS:\n\n1. Load your PRODUCT image in node 5 (reference only)\n2. Load your HAND/BACKGROUND image in node 6\n3. Right-click node 6 → 'Open in Mask Editor'\n4. Paint where you want the product to appear in the hand\n5. Update the positive prompt (node 3) with your product description\n6. Keep 'maintain the [product] features' at the start\n7. Run the workflow!\n\nTips:\n- Use high-res hand images (1024x1024+)\n- Mask the exact area where product should be held\n- Adjust blend strength in node 13 if needed\n- Fine-tune color correction in node 14"], "color": "#432", "bgcolor": "#653"}], "links": [[1, 1, 0, 2, 0, "MODEL"], [2, 1, 1, 2, 1, "CLIP"], [3, 1, 2, 7, 1, "VAE"], [4, 2, 0, 8, 0, "MODEL"], [5, 2, 1, 3, 0, "CLIP"], [6, 2, 1, 4, 0, "CLIP"], [7, 3, 0, 8, 1, "CONDITIONING"], [8, 4, 0, 8, 2, "CONDITIONING"], [9, 6, 0, 7, 0, "IMAGE"], [10, 6, 1, 7, 2, "MASK"], [11, 7, 0, 8, 3, "LATENT"], [12, 8, 0, 9, 0, "LATENT"], [13, 9, 0, 10, 0, "IMAGE"], [20, 1, 2, 9, 1, "VAE"], [21, 6, 0, 12, 0, "IMAGE"], [22, 9, 0, 13, 0, "IMAGE"], [23, 12, 0, 13, 1, "IMAGE"], [24, 13, 0, 14, 0, "IMAGE"], [25, 14, 0, 11, 0, "IMAGE"]], "groups": [{"title": "Model & Prompts", "bounding": [30, 10, 1200, 520], "color": "#3f789e", "font_size": 24, "locked": false}, {"title": "Generation & Post-Processing", "bounding": [1230, 10, 1050, 600], "color": "#b58b2a", "font_size": 24, "locked": false}, {"title": "Instructions", "bounding": [30, 580, 1050, 250], "color": "#a1634a", "font_size": 24, "locked": false}], "config": {}, "extra": {"ds": {"scale": 0.7, "offset": [0, 0]}}, "version": 0.4}